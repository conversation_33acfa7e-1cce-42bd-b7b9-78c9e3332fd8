module.exports = {

"[project]/.next-internal/server/app/api/consultation/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/consultation/route.js [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// import { NextResponse } from 'next/server';
// // const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:5001';
// const BACKEND_URL = 'https://residential-rehab-bdyh.vercel.app'
// // POST - Create a new consultation request (proxy to backend)
// export async function POST(request) {
//   try {
//     const body = await request.json();
//     const response = await fetch(`${BACKEND_URL}/api/consultation`, {
//       method: 'POST',
//       headers: {
//         'Content-Type': 'application/json',
//       },
//       body: JSON.stringify(body),
//     });
//     const data = await response.json();
//     return NextResponse.json(data, { status: response.status });
//   } catch (error) {
//     console.error('Error proxying consultation request:', error);
//     return NextResponse.json(
//       {
//         success: false,
//         error: 'Failed to process request. Please try again later.'
//       },
//       { status: 500 }
//     );
//   }
// }
// // GET - Retrieve consultation requests (proxy to backend)
// export async function GET(request) {
//   try {
//     const { searchParams } = new URL(request.url);
//     const queryString = searchParams.toString();
//     const response = await fetch(`${BACKEND_URL}/api/consultation?${queryString}`, {
//       method: 'GET',
//       headers: {
//         'Content-Type': 'application/json',
//       },
//     });
//     const data = await response.json();
//     return NextResponse.json(data, { status: response.status });
//   } catch (error) {
//     console.error('Error proxying consultation GET request:', error);
//     return NextResponse.json(
//       {
//         success: false,
//         error: 'Internal server error'
//       },
//       { status: 500 }
//     );
//   }
// }
// // PATCH - Update consultation status (proxy to backend)
// export async function PATCH(request) {
//   try {
//     const body = await request.json();
//     const response = await fetch(`${BACKEND_URL}/api/consultation`, {
//       method: 'PATCH',
//       headers: {
//         'Content-Type': 'application/json',
//       },
//       body: JSON.stringify(body),
//     });
//     const data = await response.json();
//     return NextResponse.json(data, { status: response.status });
//   } catch (error) {
//     console.error('Error proxying consultation PATCH request:', error);
//     return NextResponse.json(
//       {
//         success: false,
//         error: 'Internal server error'
//       },
//       { status: 500 }
//     );
//   }
// }
__turbopack_context__.s({
    "GET": (()=>GET),
    "PATCH": (()=>PATCH),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
(()=>{
    const e = new Error("Cannot find module '@/lib/mongodb'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
(()=>{
    const e = new Error("Cannot find module '@/models/Consultation'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
;
;
;
async function POST(req) {
    try {
        await connectDB();
        const body = await req.json();
        const { name, email, phone, propertyAddress, inquiryType, message } = body;
        if (!name || !email || !phone || !inquiryType) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Missing required fields: name, email, phone, and inquiry type'
            }, {
                status: 400
            });
        }
        const emailRegex = /^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/;
        if (!emailRegex.test(email)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Invalid email address'
            }, {
                status: 400
            });
        }
        const validInquiryTypes = [
            'sell',
            'invest',
            'other'
        ];
        if (!validInquiryTypes.includes(inquiryType)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Invalid inquiry type'
            }, {
                status: 400
            });
        }
        const consultation = new Consultation({
            name: name.trim(),
            email: email.trim().toLowerCase(),
            phone: phone.trim(),
            propertyAddress: propertyAddress?.trim() || '',
            inquiryType,
            message: message?.trim() || '',
            status: 'new'
        });
        const savedConsultation = await consultation.save();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Consultation submitted successfully',
            data: {
                id: savedConsultation._id,
                name: savedConsultation.name,
                email: savedConsultation.email,
                inquiryType: savedConsultation.inquiryType,
                createdAt: savedConsultation.createdAt
            }
        }, {
            status: 201
        });
    } catch (error) {
        console.error('POST error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Server error'
        }, {
            status: 500
        });
    }
}
async function GET(req) {
    try {
        await connectDB();
        const { searchParams } = new URL(req.url);
        const page = parseInt(searchParams.get('page')) || 1;
        const limit = parseInt(searchParams.get('limit')) || 10;
        const status = searchParams.get('status');
        const inquiryType = searchParams.get('inquiryType');
        const query = {};
        if (status) query.status = status;
        if (inquiryType) query.inquiryType = inquiryType;
        const skip = (page - 1) * limit;
        const consultations = await Consultation.find(query).sort({
            createdAt: -1
        }).skip(skip).limit(limit).select('-__v');
        const total = await Consultation.countDocuments(query);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: consultations,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('GET error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Server error'
        }, {
            status: 500
        });
    }
}
async function PATCH(req) {
    try {
        await connectDB();
        const body = await req.json();
        const { id, status } = body;
        if (!id || !status) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Consultation ID and status are required'
            }, {
                status: 400
            });
        }
        const validStatuses = [
            'new',
            'contacted',
            'in-progress',
            'completed'
        ];
        if (!validStatuses.includes(status)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Invalid status value'
            }, {
                status: 400
            });
        }
        const updated = await Consultation.findByIdAndUpdate(id, {
            status,
            updatedAt: new Date()
        }, {
            new: true,
            runValidators: true
        });
        if (!updated) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                error: 'Consultation not found'
            }, {
                status: 404
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            message: 'Consultation status updated successfully',
            consultation: updated
        });
    } catch (error) {
        console.error('PATCH error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: 'Server error'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__5ddedcda._.js.map