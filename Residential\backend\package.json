{"name": "residential-rehab-backend", "version": "1.0.0", "description": "Backend API for Residential Rehab application", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "mongoose": "^8.16.3", "bcryptjs": "^3.0.2", "nodemailer": "^7.0.5", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"nodemon": "^3.0.1", "@types/node": "^20"}, "keywords": ["real-estate", "api", "backend"], "author": "", "license": "ISC"}