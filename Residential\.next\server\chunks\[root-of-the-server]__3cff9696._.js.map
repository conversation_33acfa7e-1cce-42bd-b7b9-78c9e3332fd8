{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/lib/mongodb.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/models/Consultation.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst ConsultationSchema = new mongoose.Schema({\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [100, 'Name cannot be more than 100 characters']\n  },\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    trim: true,\n    lowercase: true,\n    match: [/^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/, 'Please enter a valid email']\n  },\n  phone: {\n    type: String,\n    required: [true, 'Phone number is required'],\n    trim: true,\n    maxlength: [20, 'Phone number cannot be more than 20 characters']\n  },\n  propertyAddress: {\n    type: String,\n    trim: true,\n    maxlength: [200, 'Property address cannot be more than 200 characters']\n  },\n  inquiryType: {\n    type: String,\n    required: [true, 'Inquiry type is required'],\n    enum: ['sell', 'invest', 'other'],\n    default: 'sell'\n  },\n  message: {\n    type: String,\n    trim: true,\n    maxlength: [1000, 'Message cannot be more than 1000 characters']\n  },\n  status: {\n    type: String,\n    enum: ['new', 'contacted', 'in-progress', 'completed'],\n    default: 'new'\n  },\n  createdAt: {\n    type: Date,\n    default: Date.now\n  },\n  updatedAt: {\n    type: Date,\n    default: Date.now\n  }\n});\n\n// Update the updatedAt field before saving\nConsultationSchema.pre('save', function(next) {\n  this.updatedAt = Date.now();\n  next();\n});\n\n// Create indexes for better query performance\nConsultationSchema.index({ email: 1 });\nConsultationSchema.index({ createdAt: -1 });\nConsultationSchema.index({ status: 1 });\n\nexport default mongoose.models.Consultation || mongoose.model('Consultation', ConsultationSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,qBAAqB,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IAC7C,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAK;SAA0C;IAC7D;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,MAAM;QACN,WAAW;QACX,OAAO;YAAC;YAA+C;SAA6B;IACtF;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;QACN,WAAW;YAAC;YAAI;SAAiD;IACnE;IACA,iBAAiB;QACf,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAAsD;IACzE;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;YAAC;YAAQ;YAAU;SAAQ;QACjC,SAAS;IACX;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAA8C;IAClE;IACA,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAO;YAAa;YAAe;SAAY;QACtD,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,WAAW;QACT,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;AACF;AAEA,2CAA2C;AAC3C,mBAAmB,GAAG,CAAC,QAAQ,SAAS,IAAI;IAC1C,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG;IACzB;AACF;AAEA,8CAA8C;AAC9C,mBAAmB,KAAK,CAAC;IAAE,OAAO;AAAE;AACpC,mBAAmB,KAAK,CAAC;IAAE,WAAW,CAAC;AAAE;AACzC,mBAAmB,KAAK,CAAC;IAAE,QAAQ;AAAE;uCAEtB,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,YAAY,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,gBAAgB", "debugId": null}}, {"offset": {"line": 228, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/app/api/consultation/route.js"], "sourcesContent": ["// import { NextResponse } from 'next/server';\r\n\r\n// // const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:5001';\r\n// const BACKEND_URL = 'https://residential-rehab-bdyh.vercel.app'\r\n\r\n// // POST - Create a new consultation request (proxy to backend)\r\n// export async function POST(request) {\r\n//   try {\r\n//     const body = await request.json();\r\n\r\n//     const response = await fetch(`${BACKEND_URL}/api/consultation`, {\r\n//       method: 'POST',\r\n//       headers: {\r\n//         'Content-Type': 'application/json',\r\n//       },\r\n//       body: JSON.stringify(body),\r\n//     });\r\n\r\n//     const data = await response.json();\r\n\r\n//     return NextResponse.json(data, { status: response.status });\r\n\r\n//   } catch (error) {\r\n//     console.error('Error proxying consultation request:', error);\r\n//     return NextResponse.json(\r\n//       {\r\n//         success: false,\r\n//         error: 'Failed to process request. Please try again later.'\r\n//       },\r\n//       { status: 500 }\r\n//     );\r\n//   }\r\n// }\r\n\r\n// // GET - Retrieve consultation requests (proxy to backend)\r\n// export async function GET(request) {\r\n//   try {\r\n//     const { searchParams } = new URL(request.url);\r\n//     const queryString = searchParams.toString();\r\n\r\n//     const response = await fetch(`${BACKEND_URL}/api/consultation?${queryString}`, {\r\n//       method: 'GET',\r\n//       headers: {\r\n//         'Content-Type': 'application/json',\r\n//       },\r\n//     });\r\n\r\n//     const data = await response.json();\r\n\r\n//     return NextResponse.json(data, { status: response.status });\r\n\r\n//   } catch (error) {\r\n//     console.error('Error proxying consultation GET request:', error);\r\n//     return NextResponse.json(\r\n//       {\r\n//         success: false,\r\n//         error: 'Internal server error'\r\n//       },\r\n//       { status: 500 }\r\n//     );\r\n//   }\r\n// }\r\n\r\n// // PATCH - Update consultation status (proxy to backend)\r\n// export async function PATCH(request) {\r\n//   try {\r\n//     const body = await request.json();\r\n\r\n//     const response = await fetch(`${BACKEND_URL}/api/consultation`, {\r\n//       method: 'PATCH',\r\n//       headers: {\r\n//         'Content-Type': 'application/json',\r\n//       },\r\n//       body: JSON.stringify(body),\r\n//     });\r\n\r\n//     const data = await response.json();\r\n\r\n//     return NextResponse.json(data, { status: response.status });\r\n\r\n//   } catch (error) {\r\n//     console.error('Error proxying consultation PATCH request:', error);\r\n//     return NextResponse.json(\r\n//       {\r\n//         success: false,\r\n//         error: 'Internal server error'\r\n//       },\r\n//       { status: 500 }\r\n//     );\r\n//   }\r\n// }\r\n\r\nimport { NextResponse } from 'next/server';\r\nimport connectDB from '@/lib/mongodb.js';\r\nimport Consultation from '@/models/Consultation.js';\r\n\r\n// ---------------- POST: Create consultation ----------------\r\nexport async function POST(req) {\r\n  try {\r\n    await connectDB();\r\n    const body = await req.json();\r\n    const { name, email, phone, propertyAddress, inquiryType, message } = body;\r\n\r\n    if (!name || !email || !phone || !inquiryType) {\r\n      return NextResponse.json(\r\n        { success: false, error: 'Missing required fields: name, email, phone, and inquiry type' },\r\n        { status: 400 }\r\n      );\r\n    }\r\n\r\n    const emailRegex = /^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/;\r\n    if (!emailRegex.test(email)) {\r\n      return NextResponse.json({ success: false, error: 'Invalid email address' }, { status: 400 });\r\n    }\r\n\r\n    const validInquiryTypes = ['sell', 'invest', 'other'];\r\n    if (!validInquiryTypes.includes(inquiryType)) {\r\n      return NextResponse.json({ success: false, error: 'Invalid inquiry type' }, { status: 400 });\r\n    }\r\n\r\n    const consultation = new Consultation({\r\n      name: name.trim(),\r\n      email: email.trim().toLowerCase(),\r\n      phone: phone.trim(),\r\n      propertyAddress: propertyAddress?.trim() || '',\r\n      inquiryType,\r\n      message: message?.trim() || '',\r\n      status: 'new',\r\n    });\r\n\r\n    const savedConsultation = await consultation.save();\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      message: 'Consultation submitted successfully',\r\n      data: {\r\n        id: savedConsultation._id,\r\n        name: savedConsultation.name,\r\n        email: savedConsultation.email,\r\n        inquiryType: savedConsultation.inquiryType,\r\n        createdAt: savedConsultation.createdAt,\r\n      },\r\n    }, { status: 201 });\r\n\r\n  } catch (error) {\r\n    console.error('POST error:', error);\r\n    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });\r\n  }\r\n}\r\n\r\n// ---------------- GET: List consultations ----------------\r\nexport async function GET(req) {\r\n  try {\r\n    await connectDB();\r\n\r\n    const { searchParams } = new URL(req.url);\r\n    const page = parseInt(searchParams.get('page')) || 1;\r\n    const limit = parseInt(searchParams.get('limit')) || 10;\r\n    const status = searchParams.get('status');\r\n    const inquiryType = searchParams.get('inquiryType');\r\n\r\n    const query = {};\r\n    if (status) query.status = status;\r\n    if (inquiryType) query.inquiryType = inquiryType;\r\n\r\n    const skip = (page - 1) * limit;\r\n\r\n    const consultations = await Consultation.find(query)\r\n      .sort({ createdAt: -1 })\r\n      .skip(skip)\r\n      .limit(limit)\r\n      .select('-__v');\r\n\r\n    const total = await Consultation.countDocuments(query);\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      data: consultations,\r\n      pagination: {\r\n        page,\r\n        limit,\r\n        total,\r\n        pages: Math.ceil(total / limit),\r\n      },\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error('GET error:', error);\r\n    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });\r\n  }\r\n}\r\n\r\n// ---------------- PATCH: Update consultation status ----------------\r\nexport async function PATCH(req) {\r\n  try {\r\n    await connectDB();\r\n    const body = await req.json();\r\n    const { id, status } = body;\r\n\r\n    if (!id || !status) {\r\n      return NextResponse.json({ success: false, error: 'Consultation ID and status are required' }, { status: 400 });\r\n    }\r\n\r\n    const validStatuses = ['new', 'contacted', 'in-progress', 'completed'];\r\n    if (!validStatuses.includes(status)) {\r\n      return NextResponse.json({ success: false, error: 'Invalid status value' }, { status: 400 });\r\n    }\r\n\r\n    const updated = await Consultation.findByIdAndUpdate(\r\n      id,\r\n      { status, updatedAt: new Date() },\r\n      { new: true, runValidators: true }\r\n    );\r\n\r\n    if (!updated) {\r\n      return NextResponse.json({ success: false, error: 'Consultation not found' }, { status: 404 });\r\n    }\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      message: 'Consultation status updated successfully',\r\n      consultation: updated,\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error('PATCH error:', error);\r\n    return NextResponse.json({ success: false, error: 'Server error' }, { status: 500 });\r\n  }\r\n}\r\n\r\n"], "names": [], "mappings": "AAAA,8CAA8C;AAE9C,6EAA6E;AAC7E,kEAAkE;AAElE,iEAAiE;AACjE,wCAAwC;AACxC,UAAU;AACV,yCAAyC;AAEzC,wEAAwE;AACxE,wBAAwB;AACxB,mBAAmB;AACnB,8CAA8C;AAC9C,WAAW;AACX,oCAAoC;AACpC,UAAU;AAEV,0CAA0C;AAE1C,mEAAmE;AAEnE,sBAAsB;AACtB,oEAAoE;AACpE,gCAAgC;AAChC,UAAU;AACV,0BAA0B;AAC1B,sEAAsE;AACtE,WAAW;AACX,wBAAwB;AACxB,SAAS;AACT,MAAM;AACN,IAAI;AAEJ,6DAA6D;AAC7D,uCAAuC;AACvC,UAAU;AACV,qDAAqD;AACrD,mDAAmD;AAEnD,uFAAuF;AACvF,uBAAuB;AACvB,mBAAmB;AACnB,8CAA8C;AAC9C,WAAW;AACX,UAAU;AAEV,0CAA0C;AAE1C,mEAAmE;AAEnE,sBAAsB;AACtB,wEAAwE;AACxE,gCAAgC;AAChC,UAAU;AACV,0BAA0B;AAC1B,yCAAyC;AACzC,WAAW;AACX,wBAAwB;AACxB,SAAS;AACT,MAAM;AACN,IAAI;AAEJ,2DAA2D;AAC3D,yCAAyC;AACzC,UAAU;AACV,yCAAyC;AAEzC,wEAAwE;AACxE,yBAAyB;AACzB,mBAAmB;AACnB,8CAA8C;AAC9C,WAAW;AACX,oCAAoC;AACpC,UAAU;AAEV,0CAA0C;AAE1C,mEAAmE;AAEnE,sBAAsB;AACtB,0EAA0E;AAC1E,gCAAgC;AAChC,UAAU;AACV,0BAA0B;AAC1B,yCAAyC;AACzC,WAAW;AACX,wBAAwB;AACxB,SAAS;AACT,MAAM;AACN,IAAI;;;;;;AAEJ;AACA;AACA;;;;AAGO,eAAe,KAAK,GAAG;IAC5B,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG;QAEtE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAgE,GACzF;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAwB,GAAG;gBAAE,QAAQ;YAAI;QAC7F;QAEA,MAAM,oBAAoB;YAAC;YAAQ;YAAU;SAAQ;QACrD,IAAI,CAAC,kBAAkB,QAAQ,CAAC,cAAc;YAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAuB,GAAG;gBAAE,QAAQ;YAAI;QAC5F;QAEA,MAAM,eAAe,IAAI,+HAAA,CAAA,UAAY,CAAC;YACpC,MAAM,KAAK,IAAI;YACf,OAAO,MAAM,IAAI,GAAG,WAAW;YAC/B,OAAO,MAAM,IAAI;YACjB,iBAAiB,iBAAiB,UAAU;YAC5C;YACA,SAAS,SAAS,UAAU;YAC5B,QAAQ;QACV;QAEA,MAAM,oBAAoB,MAAM,aAAa,IAAI;QAEjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;gBACJ,IAAI,kBAAkB,GAAG;gBACzB,MAAM,kBAAkB,IAAI;gBAC5B,OAAO,kBAAkB,KAAK;gBAC9B,aAAa,kBAAkB,WAAW;gBAC1C,WAAW,kBAAkB,SAAS;YACxC;QACF,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,eAAe;QAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAO,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpF;AACF;AAGO,eAAe,IAAI,GAAG;IAC3B,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG;QACxC,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,YAAY;QACnD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,aAAa;QACrD,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,cAAc,aAAa,GAAG,CAAC;QAErC,MAAM,QAAQ,CAAC;QACf,IAAI,QAAQ,MAAM,MAAM,GAAG;QAC3B,IAAI,aAAa,MAAM,WAAW,GAAG;QAErC,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,MAAM,gBAAgB,MAAM,+HAAA,CAAA,UAAY,CAAC,IAAI,CAAC,OAC3C,IAAI,CAAC;YAAE,WAAW,CAAC;QAAE,GACrB,IAAI,CAAC,MACL,KAAK,CAAC,OACN,MAAM,CAAC;QAEV,MAAM,QAAQ,MAAM,+HAAA,CAAA,UAAY,CAAC,cAAc,CAAC;QAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAO,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpF;AACF;AAGO,eAAe,MAAM,GAAG;IAC7B,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG;QAEvB,IAAI,CAAC,MAAM,CAAC,QAAQ;YAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAA0C,GAAG;gBAAE,QAAQ;YAAI;QAC/G;QAEA,MAAM,gBAAgB;YAAC;YAAO;YAAa;YAAe;SAAY;QACtE,IAAI,CAAC,cAAc,QAAQ,CAAC,SAAS;YACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAuB,GAAG;gBAAE,QAAQ;YAAI;QAC5F;QAEA,MAAM,UAAU,MAAM,+HAAA,CAAA,UAAY,CAAC,iBAAiB,CAClD,IACA;YAAE;YAAQ,WAAW,IAAI;QAAO,GAChC;YAAE,KAAK;YAAM,eAAe;QAAK;QAGnC,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAAG;gBAAE,QAAQ;YAAI;QAC9F;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,cAAc;QAChB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAO,OAAO;QAAe,GAAG;YAAE,QAAQ;QAAI;IACpF;AACF", "debugId": null}}]}