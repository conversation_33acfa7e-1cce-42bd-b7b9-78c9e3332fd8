{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/lib/mongodb.js"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MAAM;AAClB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/models/Admin.js"], "sourcesContent": ["import mongoose from 'mongoose';\nimport bcrypt from 'bcryptjs';\n\nconst AdminSchema = new mongoose.Schema({\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [100, 'Name cannot be more than 100 characters']\n  },\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    trim: true,\n    lowercase: true,\n    unique: true,\n    match: [/^\\w+([.-]?\\w+)*@\\w+([.-]?\\w+)*(\\.\\w{2,3})+$/, 'Please enter a valid email']\n  },\n  password: {\n    type: String,\n    required: [true, 'Password is required'],\n    minlength: [6, 'Password must be at least 6 characters']\n  },\n  createdAt: {\n    type: Date,\n    default: Date.now\n  },\n  updatedAt: {\n    type: Date,\n    default: Date.now\n  },\n  lastLogin: {\n    type: Date\n  }\n});\n\n// Hash password before saving\nAdminSchema.pre('save', async function(next) {\n  // Only hash the password if it has been modified (or is new)\n  if (!this.isModified('password')) return next();\n  \n  try {\n    // Hash password with cost of 12\n    const hashedPassword = await bcrypt.hash(this.password, 12);\n    this.password = hashedPassword;\n    next();\n  } catch (error) {\n    next(error);\n  }\n});\n\n// Update the updatedAt field before saving\nAdminSchema.pre('save', function(next) {\n  this.updatedAt = Date.now();\n  next();\n});\n\n// Instance method to check password\nAdminSchema.methods.comparePassword = async function(candidatePassword) {\n  return bcrypt.compare(candidatePassword, this.password);\n};\n\n// Create indexes for better query performance\nAdminSchema.index({ email: 1 });\n\nexport default mongoose.models.Admin || mongoose.model('Admin', AdminSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,cAAc,IAAI,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC;IACtC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAK;SAA0C;IAC7D;IACA,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,MAAM;QACN,WAAW;QACX,QAAQ;QACR,OAAO;YAAC;YAA+C;SAA6B;IACtF;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,WAAW;YAAC;YAAG;SAAyC;IAC1D;IACA,WAAW;QACT,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,WAAW;QACT,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,WAAW;QACT,MAAM;IACR;AACF;AAEA,8BAA8B;AAC9B,YAAY,GAAG,CAAC,QAAQ,eAAe,IAAI;IACzC,6DAA6D;IAC7D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEzC,IAAI;QACF,gCAAgC;QAChC,MAAM,iBAAiB,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;QACxD,IAAI,CAAC,QAAQ,GAAG;QAChB;IACF,EAAE,OAAO,OAAO;QACd,KAAK;IACP;AACF;AAEA,2CAA2C;AAC3C,YAAY,GAAG,CAAC,QAAQ,SAAS,IAAI;IACnC,IAAI,CAAC,SAAS,GAAG,KAAK,GAAG;IACzB;AACF;AAEA,oCAAoC;AACpC,YAAY,OAAO,CAAC,eAAe,GAAG,eAAe,iBAAiB;IACpE,OAAO,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,mBAAmB,IAAI,CAAC,QAAQ;AACxD;AAEA,8CAA8C;AAC9C,YAAY,KAAK,CAAC;IAAE,OAAO;AAAE;uCAEd,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,SAAS", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/app/api/admin/auth/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\r\nimport bcrypt from 'bcryptjs';\r\nimport jwt from 'jsonwebtoken';\r\nimport connectDB from '@/lib/mongodb.js';\r\nimport Admin from '@/models/Admin.js';\r\n\r\n// POST - Admin Login\r\nexport async function POST(request) {\r\n  try {\r\n    await connectDB();\r\n\r\n    const body = await request.json();\r\n    const { email, password } = body;\r\n\r\n    if (!email || !password) {\r\n      return NextResponse.json({\r\n        success: false,\r\n        error: 'Email and password are required'\r\n      }, { status: 400 });\r\n    }\r\n\r\n    // Find admin by email\r\n    const admin = await Admin.findOne({ email: email.toLowerCase() });\r\n    if (!admin) {\r\n      return NextResponse.json({\r\n        success: false,\r\n        error: 'Invalid credentials'\r\n      }, { status: 401 });\r\n    }\r\n\r\n    // Check password\r\n    const isValidPassword = await bcrypt.compare(password, admin.password);\r\n    if (!isValidPassword) {\r\n      return NextResponse.json({\r\n        success: false,\r\n        error: 'Invalid credentials'\r\n      }, { status: 401 });\r\n    }\r\n\r\n    // Generate JWT token\r\n    const token = jwt.sign(\r\n      { adminId: admin._id, email: admin.email },\r\n      process.env.JWT_SECRET || 'fallback-secret',\r\n      { expiresIn: '24h' }\r\n    );\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      message: 'Login successful',\r\n      token,\r\n      admin: {\r\n        id: admin._id,\r\n        email: admin.email,\r\n        name: admin.name\r\n      }\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error('Admin login error:', error);\r\n    return NextResponse.json({\r\n      success: false,\r\n      error: 'Internal server error'\r\n    }, { status: 500 });\r\n  }\r\n}\r\n\r\n// GET - Check if admin exists\r\nexport async function GET() {\r\n  try {\r\n    await connectDB();\r\n\r\n    const adminCount = await Admin.countDocuments();\r\n\r\n    return NextResponse.json({\r\n      success: true,\r\n      adminExists: adminCount > 0\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error('Error checking admin existence:', error);\r\n    return NextResponse.json({\r\n      success: false,\r\n      error: 'Internal server error'\r\n    }, { status: 500 });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAGO,eAAe,KAAK,OAAO;IAChC,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;QAE5B,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,sBAAsB;QACtB,MAAM,QAAQ,MAAM,wHAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAAE,OAAO,MAAM,WAAW;QAAG;QAC/D,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,iBAAiB;QACjB,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,MAAM,QAAQ;QACrE,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,qBAAqB;QACrB,MAAM,QAAQ,uIAAA,CAAA,UAAG,CAAC,IAAI,CACpB;YAAE,SAAS,MAAM,GAAG;YAAE,OAAO,MAAM,KAAK;QAAC,GACzC,QAAQ,GAAG,CAAC,UAAU,IAAI,mBAC1B;YAAE,WAAW;QAAM;QAGrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT;YACA,OAAO;gBACL,IAAI,MAAM,GAAG;gBACb,OAAO,MAAM,KAAK;gBAClB,MAAM,MAAM,IAAI;YAClB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,aAAa,MAAM,wHAAA,CAAA,UAAK,CAAC,cAAc;QAE7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,aAAa,aAAa;QAC5B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}