{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Residential%20Rehab/Residential_Rehab/Residential/src/app/api/test/route.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\r\n\r\nexport async function GET() {\r\n  return NextResponse.json({\r\n    success: true,\r\n    message: 'API is working!',\r\n    timestamp: new Date().toISOString()\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACvB,SAAS;QACT,SAAS;QACT,WAAW,IAAI,OAAO,WAAW;IACnC;AACF", "debugId": null}}]}